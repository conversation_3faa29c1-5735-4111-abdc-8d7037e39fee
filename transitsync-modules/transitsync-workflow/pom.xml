<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.transitsync</groupId>
        <artifactId>transitsync-modules</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>transitsync-workflow</artifactId>

    <description>
        工作流模�?
    </description>

    <dependencies>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-sse</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-idempotent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-translation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync</groupId>
            <artifactId>transitsync-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync.warm</groupId>
            <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.transitsync.warm</groupId>
            <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
        </dependency>
    </dependencies>

</project>

