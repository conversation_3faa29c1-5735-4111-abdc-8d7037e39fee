package com.transitsync;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * TransitSync 公交调度平台启动程序
 *
 * <AUTHOR> Team
 */

@SpringBootApplication
public class TransitSyncApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(TransitSyncApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  TransitSync 公交调度平台启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
