package com.transitsync;

import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * TransitSync web容器中进行部署
 *
 * <AUTHOR> Team
 */
public class TransitSyncServletInitializer extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(TransitSyncApplication.class);
    }

}
